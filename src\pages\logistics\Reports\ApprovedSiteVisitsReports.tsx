// src/pages/Reports/ApprovedSiteVisitsReport.tsx
import React, { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { CalendarDays, MapPin, User, Building2, CheckCircle2, Clock, XCircle, Users, Car, MessageSquare, Briefcase } from "lucide-react";
import { useGetSpecialBookingsQuery } from "@/redux/slices/logistics";

const ApprovedSiteVisitsReport: React.FC<{ data: any }> = ({ data }) => {
  const [combinedResults, setCombinedResults] = useState<any[]>([]);

  // Get special assignments with Approved status
  const { data: specialAssignmentsData } = useGetSpecialBookingsQuery({
    status: 'Approved',
    page: 1,
    page_size: 1000,
  });

  // Combine site visits and special assignments
  useEffect(() => {
    const siteVisits = data?.results || data?.data?.results || [];
    const specialAssignments = specialAssignmentsData?.data?.results || specialAssignmentsData?.results || [];

    // Transform special assignments to match site visit structure
    const transformedAssignments = specialAssignments.map((assignment: any) => ({
      ...assignment,
      pickup_date: assignment.reservation_date,
      pickup_time: assignment.reservation_time,
      pickup_location: assignment.pickup_location,
      project: assignment.destination,
      marketer: assignment.assigned_to?.name || assignment.assigned_to,
      is_special_assignment: true,
    }));

    setCombinedResults([...siteVisits, ...transformedAssignments]);
  }, [data, specialAssignmentsData]);

  if (!combinedResults?.length) {
    return (
      <Card className="w-full">
        <CardContent className="flex flex-col items-center justify-center py-12">
          <CheckCircle2 className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold text-muted-foreground">No Approved Visits</h3>
          <p className="text-sm text-muted-foreground mt-2">
            No approved site visits or special assignments found for the selected date range.
          </p>
        </CardContent>
      </Card>
    );
  }

  const getStatusBadge = (status: string) => {
    const statusLower = status?.toLowerCase();
    switch (statusLower) {
      case 'approved':
        return <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-200"><CheckCircle2 className="w-3 h-3 mr-1" />{status}</Badge>;
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200"><Clock className="w-3 h-3 mr-1" />{status}</Badge>;
      case 'rejected':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />{status}</Badge>;
      default:
        return <Badge variant="outline">{status || 'Unknown'}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return '-';
    try {
      const time = timeString.includes('T') ? new Date(timeString) : new Date(`2000-01-01T${timeString}`);
      return time.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    } catch {
      return timeString;
    }
  };

  const getClientCount = (item: any) => {
    if (item.site_visit_client && Array.isArray(item.site_visit_client)) {
      return item.site_visit_client.length;
    }
    if (item.clients && Array.isArray(item.clients)) {
      return item.clients.length;
    }
    return item.clients || item.client_count || 0;
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2">
          <CheckCircle2 className="h-5 w-5 text-green-600" />
          Approved Visits Report
        </CardTitle>
        <CardDescription>
          Overview of all approved site visits and special assignments with detailed information
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow className="bg-muted/50">
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <CalendarDays className="h-4 w-4" />
                    Date & Time
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <Briefcase className="h-4 w-4" />
                    Type
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Marketer
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    No. of Clients
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4" />
                    Project/Destination
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Driver
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <Car className="h-4 w-4" />
                    Vehicle
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    Pickup Location
                  </div>
                </TableHead>
                <TableHead className="font-semibold">Status</TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4" />
                    Remarks
                  </div>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {combinedResults.map((visit: any, i: number) => (
                <TableRow key={visit.id || i} className="hover:bg-muted/30 transition-colors">
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <CalendarDays className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">{formatDate(visit.pickup_date)}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        <span>{formatTime(visit.pickup_time)}</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {visit.is_special_assignment ? (
                        <Badge variant="secondary" className="bg-orange-100 text-orange-800 hover:bg-orange-200">
                          <Briefcase className="w-3 h-3 mr-1" />
                          Special Assignment
                        </Badge>
                      ) : (
                        <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-200">
                          <Building2 className="w-3 h-3 mr-1" />
                          Site Visit
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <User className="h-4 w-4 text-primary" />
                      </div>
                      <span className="font-medium">{visit.marketer || visit.marketer_name || '-'}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{getClientCount(visit)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Building2 className="h-4 w-4 text-muted-foreground" />
                      <span>{visit.project || visit.project_name || visit.special_assignment_destination || '-'}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <User className="h-4 w-4 text-primary" />
                      </div>
                      <span className="font-medium">{visit.driver || visit.driver_name || '-'}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Car className="h-4 w-4 text-muted-foreground" />
                      <div className="text-sm">
                        <div className="font-medium">
                          {[visit.vehicle_make, visit.vehicle_model]
                            .filter(Boolean)
                            .join(" ") || visit.vehicle || '-'}
                        </div>
                        {visit.vehicle_registration && (
                          <div className="text-xs font-semibold text-primary bg-primary/10 px-2 py-1 rounded mt-1">
                            {visit.vehicle_registration}
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{visit.pickup_location || visit.location || '-'}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(visit.status)}
                  </TableCell>
                  <TableCell>
                    <div className="max-w-xs">
                      {visit.remarks ? (
                        <div className="flex items-start gap-2">
                          <MessageSquare className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-muted-foreground">{visit.remarks}</span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <div className="mt-4 text-sm text-muted-foreground">
          Showing {combinedResults.length} approved visit{combinedResults.length !== 1 ? 's' : ''} (site visits & special assignments)
        </div>
      </CardContent>
    </Card>
  );
};
export default ApprovedSiteVisitsReport;
