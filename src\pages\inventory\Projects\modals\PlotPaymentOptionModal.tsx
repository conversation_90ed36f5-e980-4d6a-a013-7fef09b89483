import BaseModal from "@/components/custom/modals/BaseModal";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useLazyGetPlotPaymentOptionsQuery } from "@/pages/OfferLetter/api/offerLetterApi";
import { useState } from "react";
import { toast } from "sonner";

type Props = {
  openModal: boolean;
  setOpenModal: (e: boolean) => void;
};

const PlotPaymentOptionModal = ({ openModal, setOpenModal }: Props) => {
  const [plotNumber, setPlotNumber] = useState("");
  const [fetchPlotPayment, { data, isLoading: loading, isError, error }] =
    useLazyGetPlotPaymentOptionsQuery();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!plotNumber.trim()) {
      toast.error("Please enter a plot number");
      return;
    }
    try {
      const res: any = await fetchPlotPayment({ PLOT_NO: plotNumber });
      if (res) {
        // toast.success("Payment options fetched successfully");
        console.log("Plot number submitted:", plotNumber, res);
      }
    } catch (error) {
      console.log("errpr", error);
      return;
    }
    // Handle submission logic here
  };

  return (
    <BaseModal
      isOpen={openModal}
      onOpenChange={setOpenModal}
      size="xl"
      title="Plot Payment Option"
      description="Enter plot number to view payment options"
    >
      <form
        onSubmit={handleSubmit}
        className=" py-4 grid grid-cols-1 md:grid-cols-4 gap-4 items-center"
      >
        <div className="md:col-span-3 col-span-1 my-1">
          {/* <label htmlFor="plotNumber">Plot Number *</label> */}
          <Input
            id="plotNumber"
            value={plotNumber}
            onChange={(e) => setPlotNumber(e.target.value)}
            placeholder="Enter plot number"
            required
          />
        </div>
        <Button type="submit" className="my-1">
          View Payment Options
        </Button>
      </form>
      <div className="my-4">
        {loading ? (
          <div className="flex justify-center items-center w-full h-full">
            <SpinnerTemp type="spinner-double" size="md" />
          </div>
        ) : isError ? (
          <p className="text-red-500 border border-red-500 p-2 rounded-lg text-center">
            {error?.data?.error || "Failed to fetch payment options"}
          </p>
        ) : (
          data?.data?.results.map((paymentOption: any) => (
            <div key={paymentOption.id}>
              <p>{paymentOption?.PAYMENT_OPTION}</p>
            </div>
          ))
        )}
      </div>
    </BaseModal>
  );
};

export default PlotPaymentOptionModal;
